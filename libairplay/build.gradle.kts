plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
}

android {
    namespace = "com.kescoode.airplay"
    compileSdk = libs.versions.sdkversion.get().toInt()

    defaultConfig {
        minSdk = libs.versions.minversion.get().toInt()
        targetSdk = libs.versions.sdkversion.get().toInt()

        consumerProguardFiles("consumer-rules.pro")

        externalNativeBuild {
            cmake {
                abiFilters.addAll(listOf("armeabi-v7a", "arm64-v8a"))
                arguments.add("-DANDROID_PLATFORM=android-21")
                arguments.add("-DANDROID_STL=c++_shared")
            }
        }
    }
    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlin {
        jvmToolchain(17)
    }

    externalNativeBuild {
        cmake {
            path = file("../airplay/CMakeLists.txt")
        }
    }
}
