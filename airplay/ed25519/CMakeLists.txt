# CMakeLists.txt for libed25519
# Replaces ed25519/Android.mk

# Define source files
set(ED25519_SOURCES
    add_scalar.c
    fe.c
    ge.c
    key_exchange.c
    keypair.c
    sc.c
    seed.c
    sha512.c
    sign.c
    verify.c
)

# Create static library
add_library(ed25519 STATIC ${ED25519_SOURCES})

# Set target properties
set_target_properties(ed25519 PROPERTIES
    OUTPUT_NAME "ed25519"
)

# Compiler flags specific to this module
target_compile_options(ed25519 PRIVATE
    -O3
    -W
    -Wall
)

# Include directories
target_include_directories(ed25519 PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_SOURCE_DIR}/../airplay
)
