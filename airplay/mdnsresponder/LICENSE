The majority of the source code in the mDNSResponder project is licensed
under the terms of the Apache License, Version 2.0, available from:
   <http://www.apache.org/licenses/LICENSE-2.0>

To accommodate license compatibility with the widest possible range
of client code licenses, the shared library code, which is linked
at runtime into the same address space as the client using it, is
licensed under the terms of the "Three-Clause BSD License".

The Linux Name Service Switch code, contributed by National ICT
Australia Ltd (NICTA) is licensed under the terms of the NICTA Public
Software Licence (which is substantially similar to the "Three-Clause
BSD License", with some additional language pertaining to Australian law).
