.\"
.\" See section LICENSE for license information.
.\"
.Dd June 15, 2004
.Dt LIBNSS_MDNS 8
.Os
.Sh NAME
.Nm libnss_mdns
.Nd name service switch module to support Apple mdnsd
.Sh DESCRIPTION
The
.Nm
shared library implements a name service switch module to interface with Apple
mdnsd.  It is enabled by adding
.Ql mdns
to the
.Ql hosts:
line of
.Xr nsswitch.conf 5 .
This will cause calls to
.Xr gethostbyname 3 ,
.Xr gethostbyname2 3
and
.Xr gethostbyaddr 3
to include mdnsd in their lookup path.
.Pp
The
.Nm
shared library should be installed in the system library paths, typically in
.Pa /lib .
.Sh FILES
.Bl -tag -width Pa -compact
.It Pa /etc/nss_mdns.conf
configuration options
.El
.Sh EXAMPLES
In
.Pa /etc/nsswitch.conf :
.Dl hosts: files mdns dns
.Pp
This will cause the name service switch to lookup hostnames first using
.Pa /etc/hosts ,
then mdns and finally via DNS.
.Sh DIAGNOSTICS
.Nm
dumps status information via
.Xr syslog 3 .
.Sh SEE ALSO
.\" Cross-references should be ordered by section (low to high), then in
.\"     alphabetical order.
.Xr nsswitch.conf 5 ,
.Xr nss_mdns.conf 5 ,
.Xr ldconfig 8
.Pp
.Li info libc Qq Name Service Switch
.\" .Sh STANDARDS
.Sh HISTORY
.Nm
was originally written for
.An NICTA Bq http://www.nicta.com.au/ .
.Sh AUTHORS
.An "Andrew White" Bq <EMAIL>
.Sh BUGS
Return values for obscure error conditions may not match those expected by nsswitch code.
.Pp
Version 0.62 of mdnsd does not support an option to force link-local multicast lookups.
.Ql PTR
reverse lookups for non-link-local domains will not function correctly.
.Sh LICENSE
This software is licensed under the NICTA Public Source License version 1.0
.Ss NICTA Public Software Licence
Version 1.0
.Pp
Copyright 2004 National ICT Australia Ltd
.Pp
All rights reserved.
.Pp
By this licence, National ICT Australia Ltd (NICTA) grants permission,
free of charge, to any person who obtains a copy of this software
and any associated documentation files ("the Software") to use and
deal with the Software in source code and binary forms without
restriction, with or without modification, and to permit persons
to whom the Software is furnished to do so, provided that the
following conditions are met:
.Bl -bullet
.It
Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimers.
.It
Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimers in
the documentation and/or other materials provided with the
distribution.
.It
The name of NICTA may not be used to endorse or promote products
derived from this Software without specific prior written permission.
.El
.Pp
EXCEPT AS EXPRESSLY STATED IN THIS LICENCE AND TO THE FULL EXTENT
PERMITTED BY APPLICABLE LAW, THE SOFTWARE IS PROVIDED "AS-IS" AND
NICTA MAKES NO REPRESENTATIONS, WARRANTIES OR CONDITIONS OF ANY
KIND, EXPRESS OR IMPLIED, INCLUDING, WITHOUT LIMITATION, ANY
REPRESENTATIONS, WARRANTIES OR CONDITIONS REGARDING THE CONTENTS
OR ACCURACY OF THE SOFTWARE, OR OF TITLE, MERCHANTABILITY, FITNESS
FOR A PARTICULAR PURPOSE, NONINFRINGEMENT, THE ABSENCE OF LATENT
OR OTHER DEFECTS, OR THE PRESENCE OR ABSENCE OF ERRORS, WHETHER OR
NOT DISCOVERABLE.
.Pp
TO THE FULL EXTENT PERMITTED BY APPLICABLE LAW, IN NO EVENT WILL
NICTA BE LIABLE ON ANY LEGAL THEORY (INCLUDING, WITHOUT LIMITATION,
NEGLIGENCE) FOR ANY LOSS OR DAMAGE WHATSOEVER, INCLUDING (WITHOUT
LIMITATION) LOSS OF PRODUCTION OR OPERATION TIME, LOSS, DAMAGE OR
CORRUPTION OF DATA OR RECORDS; OR LOSS OF ANTICIPATED SAVINGS,
OPPORTUNITY, REVENUE, PROFIT OR GOODWILL, OR OTHER ECONOMIC LOSS;
OR ANY SPECIAL, INCIDENTAL, INDIRECT, CONSEQUENTIAL, PUNITIVE OR
EXEMPLARY DAMAGES ARISING OUT OF OR IN CONNECTION WITH THIS LICENCE,
THE SOFTWARE OR THE USE OF THE SOFTWARE, EVEN IF NICTA HAS BEEN
ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.
.Pp
If applicable legislation implies warranties or conditions, or
imposes obligations or liability on NICTA in respect of the Software
that cannot be wholly or partly excluded, restricted or modified,
NICTA's liability is limited, to the full extent permitted by the
applicable legislation, at its option, to:
.Pp
.Bl -tag -width "a." -compact
.It a.
in the case of goods, any one or more of the following:
.Bl -tag -width "iii." -compact
.It i.
the replacement of the goods or the supply of equivalent goods;
.It ii.
the repair of the goods;
.It iii.
the payment of the cost of replacing the goods or of acquiring
equivalent goods;
.It iv.
the payment of the cost of having the goods repaired; or
.El
.It b.
in the case of services:
.Bl -tag -width "iii." -compact
.It i.
the supplying of the services again; or 
.It ii.
the payment of the cost of having the services supplied again.
.El
.El
