.\"
.\" See section LICENSE for license information.
.\"
.Dd June 15, 2004
.Dt NSS_MDNS.CONF 5
.Os
.Sh NAME
.Nm nss_mdns.conf
.Nd configuration file for
.Xr libnss_mdns 8 .
.Sh DESCRIPTION
This file describes the domains that
.Xr libnss_mdns 8
is to support.  If a lookup domain is not in this list, then
.Li NSS_STATUS_NOTFOUND
will be returned to libc and processing will continue according to
.Xr nsswitch.conf 5 .
.Ss Configuration file format
Lines containing only whitespace or lines whose first non-whitespace character is
.Ql #
are ignored.  No single line may be greater than 1023 characters plus end-of-line.
.Pp
.D1 Ic domain Ar x.y.z
.Pp
Enable use of
.Xr libnss_mdns 8
to lookup DNS entries in the
.Ql x.y.z
domain.  Leading and trailing dots are dropped.
.Pp
Reverse (PTR) lookups are enabled using their DNS names.  IPv6 names use
.Qq nibble format .
.Pp
.Dl domain 254.169.in-addr.arpa
.Dl domain 0.8.e.f.ip6.arpa
.Ss Default configuration
If the configuration file cannot be found then the following is assumed.
.Bd -literal -offset indent
domain local
domain 0.8.e.f.ip6.int
domain 0.8.e.f.ip6.arpa
domain 254.169.in-addr.arpa
.Ed
.Sh SEE ALSO
.\" Cross-references should be ordered by section (low to high), then in
.\"     alphabetical order.
.Xr nsswitch.conf 5 ,
.Xr libnss_mdns 8
.\" .Sh STANDARDS
.Sh HISTORY
.Xr libnss_mdns 8
was originally written for
.An NICTA Bq http://www.nicta.com.au/ .
.Sh AUTHORS
.An "Andrew White" Bq <EMAIL>
.Sh LICENSE
This software is licensed under the NICTA Public Source License version 1.0
.Ss NICTA Public Software Licence
Version 1.0
.Pp
Copyright 2004 National ICT Australia Ltd
.Pp
All rights reserved.
.Pp
By this licence, National ICT Australia Ltd (NICTA) grants permission,
free of charge, to any person who obtains a copy of this software
and any associated documentation files ("the Software") to use and
deal with the Software in source code and binary forms without
restriction, with or without modification, and to permit persons
to whom the Software is furnished to do so, provided that the
following conditions are met:
.Bl -bullet
.It
Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimers.
.It
Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimers in
the documentation and/or other materials provided with the
distribution.
.It
The name of NICTA may not be used to endorse or promote products
derived from this Software without specific prior written permission.
.El
.Pp
EXCEPT AS EXPRESSLY STATED IN THIS LICENCE AND TO THE FULL EXTENT
PERMITTED BY APPLICABLE LAW, THE SOFTWARE IS PROVIDED "AS-IS" AND
NICTA MAKES NO REPRESENTATIONS, WARRANTIES OR CONDITIONS OF ANY
KIND, EXPRESS OR IMPLIED, INCLUDING, WITHOUT LIMITATION, ANY
REPRESENTATIONS, WARRANTIES OR CONDITIONS REGARDING THE CONTENTS
OR ACCURACY OF THE SOFTWARE, OR OF TITLE, MERCHANTABILITY, FITNESS
FOR A PARTICULAR PURPOSE, NONINFRINGEMENT, THE ABSENCE OF LATENT
OR OTHER DEFECTS, OR THE PRESENCE OR ABSENCE OF ERRORS, WHETHER OR
NOT DISCOVERABLE.
.Pp
TO THE FULL EXTENT PERMITTED BY APPLICABLE LAW, IN NO EVENT WILL
NICTA BE LIABLE ON ANY LEGAL THEORY (INCLUDING, WITHOUT LIMITATION,
NEGLIGENCE) FOR ANY LOSS OR DAMAGE WHATSOEVER, INCLUDING (WITHOUT
LIMITATION) LOSS OF PRODUCTION OR OPERATION TIME, LOSS, DAMAGE OR
CORRUPTION OF DATA OR RECORDS; OR LOSS OF ANTICIPATED SAVINGS,
OPPORTUNITY, REVENUE, PROFIT OR GOODWILL, OR OTHER ECONOMIC LOSS;
OR ANY SPECIAL, INCIDENTAL, INDIRECT, CONSEQUENTIAL, PUNITIVE OR
EXEMPLARY DAMAGES ARISING OUT OF OR IN CONNECTION WITH THIS LICENCE,
THE SOFTWARE OR THE USE OF THE SOFTWARE, EVEN IF NICTA HAS BEEN
ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.
.Pp
If applicable legislation implies warranties or conditions, or
imposes obligations or liability on NICTA in respect of the Software
that cannot be wholly or partly excluded, restricted or modified,
NICTA's liability is limited, to the full extent permitted by the
applicable legislation, at its option, to:
.Pp
.Bl -tag -width "a." -compact
.It a.
in the case of goods, any one or more of the following:
.Bl -tag -width "iii." -compact
.It i.
the replacement of the goods or the supply of equivalent goods;
.It ii.
the repair of the goods;
.It iii.
the payment of the cost of replacing the goods or of acquiring
equivalent goods;
.It iv.
the payment of the cost of having the goods repaired; or
.El
.It b.
in the case of services:
.Bl -tag -width "iii." -compact
.It i.
the supplying of the services again; or 
.It ii.
the payment of the cost of having the services supplied again.
.El
.El
