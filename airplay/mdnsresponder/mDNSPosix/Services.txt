#
# Example services file parsed by mDNSResponderPosix.
# 
# Lines beginning with '#' are comments/ignored.
# Blank lines indicate the end of a service record specification.
# The first character of the service name can be a '#' if you escape it with 
# backslash to distinguish if from a comment line.
# ie, "\#serviceName" will be registered as "#serviceName".
# Note that any line beginning with white space is considered a blank line.
#
# The record format is:
# 
# <service name>
# <type>.<protocol> <optional domain>
# <port number>
# <zero or more strings for the text record, one string per line>
#
# <One or more blank lines between records>
# 
# Examples shown below.

serviceName1
_afpovertcp._tcp.
548
name=val1

serviceName2
_afpovertcp._tcp. local.
548
name=val2
name2=anotherattribute

serviceName3
_afpovertcp._tcp.
548
name=val3
