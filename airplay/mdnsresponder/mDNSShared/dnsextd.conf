// ----------------------------------------------------------------------------
//
// Instructions for /etc/dnsextd.conf (this file)
//
// In most cases, you should not need to change these default options in
// the "options" section below. The dnsextd daemon will receive DNS packets
// on port 53, and forward them on as appropriate to BIND on localhost:5030.
//
// You need to edit the "zone" statement below to give the name of your
// dynamic zone that will be accepting Wide-Area Bonjour DNS updates.
//
// ----------------------------------------------------------------------------
//
// Instructions for /etc/named.conf
//
// In /etc/named.conf you will need to modify the "options" section to
// tell BIND to accept packets from localhost:5030, like this:
//
//   listen-on port 5030 { 127.0.0.1; };
//
// You also need a "zone" statement in /etc/named.conf to tell BIND the update
// policy for your dynamic zone. For example, within a small closed private
// network, you might allow anyone to perform updates. To do that, you just
// permit any and all updates coming from dnsextd on the same machine:
//
//   zone "my-dynamic-subdomain.company.com."
//     { type master; file "db.xxx"; allow-update { 127.0.0.1; }; };
//
// On a machine connected to the Internet or other large open network,
// you'll want to limit updates to only users with keys. For example,
// you could choose to allow anyone with a DNS key on your server to
// perform updates in your dynamic zone, like this:
//
//   key keyname. { algorithm hmac-md5; secret "abcdefghijklmnopqrstuv=="; };
//   zone "my-dynamic-subdomain.company.com." in
//     {
//     type master;
//     file "db.my-dynamic-subdomain.company.com";
//     update-policy { grant * wildcard *.my-dynamic-subdomain.company.com.; };
//     };
//
// You could use a single key which you give to all authorized users, but
// it is better (though more work) to create a unique key for each user.
//
// ----------------------------------------------------------------------------

options {
//  This defaults to: * port 53
//	listen-on 			port 53 { ************; 127.0.0.1; };
//  This defaults to: 127.0.0.1:5030
//	nameserver			address 127.0.0.1 port 5030;
//  This defaults to: 5533
//	private 			port 5533;
//  This defaults to: 5352
//	llq	   				port 5352;
};

zone "my-dynamic-subdomain.company.com." {
	type public;
};
