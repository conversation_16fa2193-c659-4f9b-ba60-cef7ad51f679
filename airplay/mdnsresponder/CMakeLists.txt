# CMakeLists.txt for libmdnssd
# Replaces mdnsresponder/Android.mk

# Define source files
set(MDNSSD_SOURCES
    mDNSShared/dnssd_clientlib.c
    mDNSShared/dnssd_clientstub.c
    mDNSShared/dnssd_ipc.c
)

# Create shared library
add_library(mdnssd SHARED ${MDNSSD_SOURCES})

# Set target properties
set_target_properties(mdnssd PROPERTIES
    OUTPUT_NAME "mdnssd"
)

# Compiler flags specific to this module
target_compile_options(mdnssd PRIVATE
    -O2
    -g
    -W
    -Wall
    -D__ANDROID__
    -D_GNU_SOURCE
    -DHAVE_IPV6
    -DNOT_HAVE_SA_LEN
    -DUSES_NETLINK
    -DTARGET_OS_LINUX
    -fno-strict-aliasing
    -DHAVE_LINUX
    -DMDNS_UDS_SERVERPATH="/dev/socket/mdnsd"
    -DMDNS_DEBUGMSGS=0
)
