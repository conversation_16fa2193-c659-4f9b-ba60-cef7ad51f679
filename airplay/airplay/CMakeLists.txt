# CMakeLists.txt for main libairplay
# Replaces airplay/Android.mk

# Option for embedded MDNS (equivalent to EMBEDDED_MDNS variable)
option(EMBEDDED_MDNS "Use embedded MDNS" OFF)

# Define main source files
set(AIRPLAY_SOURCES
    airplay.c
    digest.c
    fairplay.c
    http_parser.c
    http_request.c
    http_response.c
    httpd.c
    netutils.c
    raop.c
    raop_buffer.c
    raop_rtp.c
    sdp.c
    utils.c
    alac/alac.c
    aac_eld/aac_eld.c
    dmr/hand_garble.c
    dmr/modified_md5.c
    dmr/omg_hax.c
    dmr/playfair.c
    dmr/sap_hash.c
    main.c
    dnssd.c
    # JNI interface sources
    jniinterface/jnimain.cpp
    jniinterface/hwdecode.cpp
    jniinterface/ring_buffer.c
    jniinterface/parse_sps.c
    # Oboe audio output
    oboe_out/audio-output.cpp
)

# Create shared library
add_library(airplay SHARED ${AIRPLAY_SOURCES})

# Set target properties
set_target_properties(airplay PROPERTIES
    OUTPUT_NAME "airplay"
)

# Include directories
target_include_directories(airplay PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/openssl
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/../aac/libAACdec/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../aac/libAACenc/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../aac/libPCMutils/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../aac/libFDK/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../aac/libSYS/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../aac/libMpegTPDec/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../aac/libMpegTPEnc/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../aac/libSBRdec/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../aac/libSBRenc/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../aac/libFDK/aarch64/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../mdnsresponder/mDNSShared
    ${CMAKE_CURRENT_SOURCE_DIR}/../lollipop_wifi/socket_ipc
    ${CMAKE_CURRENT_SOURCE_DIR}/../ffmpeg5.1.3/include
    ${CMAKE_CURRENT_SOURCE_DIR}/oboe_out
)

# Link static libraries
target_link_libraries(airplay
    crypt
    curve25519
    ed25519
    FraunhoferAAC
    plist
    oboe
)

# Link system libraries
target_link_libraries(airplay
    OpenSLES
    log
)

# Conditional MDNS support
if(EMBEDDED_MDNS)
    target_compile_definitions(airplay PRIVATE EMBEDDED_MDNS=1)
    target_link_libraries(airplay mdnssd)
endif()

# Strip module in release builds
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    set_target_properties(airplay PROPERTIES LINK_FLAGS "-s")
endif()
