/*
 * Generated by util/mkerr.pl DO NOT EDIT
 * Copyright 1995-2018 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the OpenSSL license (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#ifndef HEADER_SM2ERR_H
# define HEADER_SM2ERR_H

# ifdef  __cplusplus
extern "C" {
# endif
int ERR_load_SM2_strings(void);
# ifdef  __cplusplus
}
# endif

/*
 * SM2 function codes.
 */
# define SM2_F_PKEY_SM2_CTRL                              274
# define SM2_F_PKEY_SM2_CTRL_STR                          275
# define SM2_F_PKEY_SM2_KEYGEN                            276
# define SM2_F_PKEY_SM2_PARAMGEN                          277
# define SM2_F_PKEY_SM2_SIGN                              278

/*
 * SM2 reason codes.
 */
# define SM2_R_ASN1_ERROR                                 115
# define SM2_R_ASN5_ERROR                                 1150
# define SM2_R_BAD_SIGNATURE                              156
# define SM2_R_BIGNUM_OUT_OF_RANGE                        144
# define SM2_R_BUFFER_TOO_SMALL                           100
# define SM2_R_COORDINATES_OUT_OF_RANGE                   146
# define SM2_R_CURVE_DOES_NOT_SUPPORT_ECDH                160
# define SM2_R_CURVE_DOES_NOT_SUPPORT_SIGNING             159
# define SM2_R_D2I_ECPKPARAMETERS_FAILURE                 117
# define SM2_R_DECODE_ERROR                               142
# define SM2_R_DISCRIMINANT_IS_ZERO                       118
# define SM2_R_EC_GROUP_NEW_BY_NAME_FAILURE               119
# define SM2_R_FIELD_TOO_LARGE                            143
# define SM2_R_GF2M_NOT_SUPPORTED                         147
# define SM2_R_GROUP2PKPARAMETERS_FAILURE                 120
# define SM2_R_I2D_ECPKPARAMETERS_FAILURE                 121
# define SM2_R_INCOMPATIBLE_OBJECTS                       101
# define SM2_R_INVALID_ARGUMENT                           112
# define SM2_R_INVALID_COMPRESSED_POINT                   110
# define SM2_R_INVALID_COMPRESSION_BIT                    109
# define SM2_R_INVALID_CURVE                              141
# define SM2_R_INVALID_DIGEST                             151
# define SM2_R_INVALID_DIGEST_TYPE                        138
# define SM2_R_INVALID_ENCODING                           102
# define SM2_R_INVALID_FIELD                              103
# define SM2_R_INVALID_FORM                               104
# define SM2_R_INVALID_GROUP_ORDER                        122
# define SM2_R_INVALID_KEY                                116
# define SM2_R_INVALID_OUTPUT_LENGTH                      161
# define SM2_R_INVALID_PEER_KEY                           133
# define SM2_R_INVALID_PENTANOMIAL_BASIS                  132
# define SM2_R_INVALID_PRIVATE_KEY                        123
# define SM2_R_INVALID_TRINOMIAL_BASIS                    137
# define SM2_R_KDF_PARAMETER_ERROR                        148
# define SM2_R_KEYS_NOT_SET                               140
# define SM2_R_MISSING_PARAMETERS                         124
# define SM2_R_MISSING_PRIVATE_KEY                        125
# define SM2_R_NEED_NEW_SETUP_VALUES                      157
# define SM2_R_NOT_A_NIST_PRIME                           135
# define SM2_R_NOT_IMPLEMENTED                            126
# define SM2_R_NOT_INITIALIZED                            111
# define SM2_R_NO_PARAMETERS_SET                          139
# define SM2_R_NO_PRIVATE_VALUE                           154
# define SM2_R_OPERATION_NOT_SUPPORTED                    152
# define SM2_R_PASSED_NULL_PARAMETER                      134
# define SM2_R_PEER_KEY_ERROR                             149
# define SM2_R_PKPARAMETERS2GROUP_FAILURE                 127
# define SM2_R_POINT_ARITHMETIC_FAILURE                   155
# define SM2_R_POINT_AT_INFINITY                          106
# define SM2_R_POINT_IS_NOT_ON_CURVE                      107
# define SM2_R_RANDOM_NUMBER_GENERATION_FAILED            158
# define SM2_R_SHARED_INFO_ERROR                          150
# define SM2_R_SLOT_FULL                                  108
# define SM2_R_UNDEFINED_GENERATOR                        113
# define SM2_R_UNDEFINED_ORDER                            128
# define SM2_R_UNKNOWN_GROUP                              129
# define SM2_R_UNKNOWN_ORDER                              114
# define SM2_R_UNSUPPORTED_FIELD                          131
# define SM2_R_WRONG_CURVE_PARAMETERS                     145
# define SM2_R_WRONG_ORDER                                130

#endif
