/*
 * Copyright (C) 2007 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

typedef enum {
    SHA_1,
} HashAlgorithm;

int get_file_hash(HashAlgorithm algorithm, const char *path,
                  char *output_string, size_t max_output_string);

int get_recursive_hash_manifest(HashAlgorithm algorithm,
                                const char *directory_path,
                                char **output_string);
