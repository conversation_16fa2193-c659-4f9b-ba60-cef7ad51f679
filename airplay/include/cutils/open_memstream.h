/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef __CUTILS_OPEN_MEMSTREAM_H__
#define __CUTILS_OPEN_MEMSTREAM_H__

#include <stdio.h>

#ifndef HAVE_OPEN_MEMSTREAM

#ifdef __cplusplus
extern "C" {
#endif

FILE* open_memstream(char** bufp, size_t* sizep);

#ifdef __cplusplus
}
#endif

#endif /*!HAVE_OPEN_MEMSTREAM*/

#endif /*__CUTILS_OPEN_MEMSTREAM_H__*/
