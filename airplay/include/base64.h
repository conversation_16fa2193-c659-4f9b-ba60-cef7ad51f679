/**
 *  Copyright (C) 2011-2012  <PERSON><PERSON>
 *
 *  This library is free software; you can redistribute it and/or
 *  modify it under the terms of the GNU Lesser General Public
 *  License as published by the Free Software Foundation; either
 *  version 2.1 of the License, or (at your option) any later version.
 *
 *  This library is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 *  Lesser General Public License for more details.
 */

#ifndef BASE64_H
#define BASE64_H

#include "os_port.h"

typedef struct base64_s base64_t;

EXP_FUNC base64_t* STDCALL base64_init(const char *charlist, int use_padding, int skip_spaces);

EXP_FUNC int STDCALL base64_encoded_length(base64_t *base64, int srclen);

EXP_FUNC int STDCALL base64_encode(base64_t *base64, char *dst, const unsigned char *src, int srclen);
EXP_FUNC int STDCALL base64_decode(base64_t *base64, unsigned char **dst, const char *src, int srclen);

EXP_FUNC void STDCALL base64_destroy(base64_t *base64);

#endif
