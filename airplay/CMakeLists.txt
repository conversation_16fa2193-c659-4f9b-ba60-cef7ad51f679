# CMakeLists.txt for AirPlay Native Library
# Replaces Android.mk and Application.mk

cmake_minimum_required(VERSION 3.18.1)

project(airplay)

# Set Android platform and ABI settings (equivalent to Application.mk)
set(CMAKE_ANDROID_API 21)
set(CMAKE_ANDROID_STL_TYPE c++_shared)

# Set C and C++ standards
set(CMAKE_C_STANDARD 99)
set(CMAKE_CXX_STANDARD 14)

# Global compiler flags
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -O3 -W -Wall -Wno-unused-variable -Wno-unused-parameter")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -D__ANDROID__ -D_GNU_SOURCE -DHAVE_IPV6 -DNOT_HAVE_SA_LEN")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -DUSES_NETLINK -DTARGET_OS_LINUX -fno-strict-aliasing")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -DHAVE_LINUX -DMDNS_UDS_SERVERPATH=\"/dev/socket/mdnsd\"")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -DMDNS_DEBUGMSGS=0 -fdiagnostics-color=always -pipe")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -D_FILE_OFFSET_BITS=64 -Winvalid-pch -D_REENTRANT")

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3 -W -Wall -Wno-unused-variable -Wno-unused-parameter")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DANDROID -fdata-sections -ffunction-sections")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -funwind-tables -fstack-protector-strong")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -no-canonical-prefixes -D_FORTIFY_SOURCE=2")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wformat -Werror=format-security -DNDEBUG0")

# Global include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include/openssl)

# Add subdirectories for static libraries (order matters for dependencies)
add_subdirectory(plist)
add_subdirectory(crypt)
add_subdirectory(curve25519)
add_subdirectory(ed25519)
add_subdirectory(aac)
add_subdirectory(oboe)
add_subdirectory(mdnsresponder)

# Add main airplay library last (depends on all others)
add_subdirectory(airplay)
