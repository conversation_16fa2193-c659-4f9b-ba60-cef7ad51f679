# CMakeLists.txt for libcurve25519
# Replaces curve25519/Android.mk

# Define source files
set(CURVE25519_SOURCES
    curve25519-donna.c
)

# Create static library
add_library(curve25519 STATIC ${CURVE25519_SOURCES})

# Set target properties
set_target_properties(curve25519 PROPERTIES
    OUTPUT_NAME "curve25519"
)

# Compiler flags specific to this module
target_compile_options(curve25519 PRIVATE
    -O3
    -W
    -Wall
)

# Include directories
target_include_directories(curve25519 PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
)
